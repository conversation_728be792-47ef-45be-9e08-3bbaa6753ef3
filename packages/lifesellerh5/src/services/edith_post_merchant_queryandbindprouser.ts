/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53796
  * @name: 移动端独立入驻-查询&绑定专业号
  * @identifier: app.api.redlife.merchant.queryandbindprouser.post
  * @version: undefined
  * @path: /app/api/redlife/merchant/queryandbindprouser
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import { http } from '@xhs/launcher'

export interface IPostMerchantQueryandbindprouserPayload {
	/** 用户id */
	userId: string
	/** 申请id */
	applyId: number
}

export interface IProUserInfo {
	/** 用户名 */
	userName?: string
	/** 头像 */
	avatar?: string
}

export interface IData {
	/** 专业号信息 */
	proUserInfo?: IProUserInfo
}

export interface IPostMerchantQueryandbindprouserResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMerchantQueryandbindprouser(payload: IPostMerchantQueryandbindprouserPayload, options = {}): Promise<IData> {
  return http.post('/app/api/redlife/merchant/queryandbindprouser', payload, { transform: false, ...options })
}
