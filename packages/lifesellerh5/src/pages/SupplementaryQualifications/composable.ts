import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from '@xhs/reds-h5-next'

// 接口
import { getQueryQualificationConfig } from '@edith/edith_get_query_qualification_config'

// 类型
import type { CategoryPickerValue } from '~/components/CategoryPicker/types'
import type { QualificationItem } from '~/components/QualificationCard/core/type'

export function useSupplementaryQualifications() {
  const router = useRouter()

  // 响应式数据
  const categoryPickerVisible = ref(false)
  const selectedCategory = ref<CategoryPickerValue | null>(null)
  const qualificationList = ref<any[]>([])
  const qualificationData = ref<Record<string, QualificationItem>>({})

  // 计算属性
  const selectedCategoryId = computed(() => {
    if (!selectedCategory.value || selectedCategory.value.length === 0) return ''
    return selectedCategory.value[selectedCategory.value.length - 1]
  })

  const selectedCategoryText = computed(() => {
    if (!selectedCategory.value || selectedCategory.value.length === 0) return ''
    // 简化显示，实际应该根据选中的类目ID获取名称
    return '融合菜'
  })

  const canSubmit = computed(() => {
    // 检查是否选择了主营品类
    if (!selectedCategory.value || selectedCategory.value.length === 0) {
      return false
    }

    // 检查资质是否填写完整
    if (qualificationList.value.length === 0) {
      return false
    }

    // 检查每个资质项是否都有必要的数据
    return qualificationList.value.every(qual => {
      const data = qualificationData.value[qual.qualificationCode]
      return data && data.mediaInfoList && data.mediaInfoList.length > 0
    })
  })

  // 方法
  const showCategoryPicker = () => {
    categoryPickerVisible.value = true
  }

  const closeCategoryPicker = () => {
    categoryPickerVisible.value = false
  }

  const handleCategoryConfirm = async (value: CategoryPickerValue | null) => {
    selectedCategory.value = value
    categoryPickerVisible.value = false

    if (value && value.length > 0) {
      await loadQualificationTypes()
    }
  }

  const loadQualificationTypes = async () => {
    if (!selectedCategoryId.value) return

    try {
      const result = await getQueryQualificationConfig({
        categoryId: selectedCategoryId.value
      })

      if (result?.qualificationGroupList) {
        const types: any[] = []
        result.qualificationGroupList.forEach(group => {
          if (group.qualificationElements) {
            group.qualificationElements.forEach(element => {
              if (element.qualificationConfig) {
                types.push(element.qualificationConfig)
              }
            })
          }
        })
        qualificationList.value = types
      }
    } catch (error: any) {
      showToast('获取资质配置失败')
    }
  }

  const getQualificationItemData = (qualificationCode: string): Record<string, QualificationItem> => {
    const item = qualificationData.value[qualificationCode]
    return item ? { [qualificationCode]: item } : {}
  }

  const handleQualificationUpdate = (qualificationCode: string, value: Record<string, QualificationItem>) => {
    if (value[qualificationCode]) {
      qualificationData.value[qualificationCode] = value[qualificationCode]
    }
  }

  const handleQualificationLoad = (loaded: boolean) => {
    console.log('资质配置加载:', loaded)
  }

  const handlePrevious = () => {
    router.back()
  }

  const handleSubmit = () => {
    if (!canSubmit.value) {
      showToast('请完善资质信息')
      return
    }

    showToast('提交成功')

    router.back()
  }

  return {
    // 数据
    categoryPickerVisible,
    selectedCategory,
    qualificationList,
    qualificationData,

    // 计算属性
    selectedCategoryId,
    selectedCategoryText,
    canSubmit,

    // 方法
    showCategoryPicker,
    closeCategoryPicker,
    handleCategoryConfirm,
    loadQualificationTypes,
    getQualificationItemData,
    handleQualificationUpdate,
    handleQualificationLoad,
    handlePrevious,
    handleSubmit
  }
}