<template>
  <div class="claim-store-div">
    <header>
      <Header></Header>
    </header>
    <main>
      <ConfigProvider :color-mode="colorMode">
        <div class="form-item-layout">
         <Form :data="formData" :rules="formRules" :layout="FormType.LAYOUT.VERTICAL" :show-submit="false" class="form">
            <article v-for="item in visibleFormItems" :key="item.valueKey" :class="['form-item', { 'form-item-layout': ['default', 'bottom'].includes(item.layout)}]">
              <!-- 文本 + 右侧icon -->
              <section v-if="item.type === 'textAndIcon'">
                <p class="title">{{ item.title }} </p>
                <div :class="['form-item-box', `border-radius-${item.layout}`]">
                  <FormItem
                    :label="item.label"
                    :name="item.valueKey"
                    :value="formData[item.valueKey]"
                    required
                    :class="['item', `border-${item.layout}`, `border-radius-${item.layout}`]"
                    :required-style="{color: 'red'}"
                  >

                    <div class="sheets" @click="textAndIconChange">
                      <div v-if="formData[item.valueKey]?.shopName" class="multiLineText">
                        <Text>{{ formData[item.valueKey]?.shopName }}</Text>
                        <Text class="multiLineText-shopAddress">{{ formData[item.valueKey]?.addressDetail }}</Text>
                      </div>
                      <Text v-else class="sheets-empty">{{ item.placeholder }}</Text>
                      <OnixIcon class="onix-icon-16" icon="arrowRightRightM"></OnixIcon>
                    </div>
                  </FormItem>
                </div>
              </section>
              <!-- 文本 + 右侧icon，点击会出现弹窗 -->
              <section v-if="item.type === 'Sheets'">
                <p class="title">{{ item.title }}</p>
                <div :class="['form-item-box', `border-radius-${item.layout}`]">
                  <FormItem
                    :label="item.label"
                    :name="item.valueKey"
                    :value="formData[item.valueKey]"
                    required
                    :class="['item', `border-${item.layout}`, `border-radius-${item.layout}`]"
                    :required-style="{color: 'red'}"
                  >
                    <div class="sheets" @click="sheetsClick">
                      <Text v-if="businessEntityText">{{ businessEntityText }}</Text>
                      <Text v-else class="sheets-empty">{{ item.placeholder }}</Text>
                      <OnixIcon class="onix-icon-16" icon="arrowRightRightM"></OnixIcon>
                    </div>
                  </FormItem>
                </div>

              </section>
              <!-- 上传图片 -->
              <section v-if="item.type === 'ImageUpload'">
                <div :class="['form-item-box', `border-radius-${item.layout}`]">
                  <FormItem
                    :label="item.label"
                    :name="item.valueKey"
                    :value="formData[item.valueKey]"
                    :required="item.required"
                    :class="['item', `border-${item.layout}`, `border-radius-${item.layout}`]"
                    :required-style="{color: 'red'}"
                  >
                    <ImageUpload
                      v-model="formData[item.valueKey]"
                      :is-preview="isPreview"
                      :max-count="item.maxCount"
                      :max-size="item.maxSize"
                      :prohibit-operation="prohibitOperation && item.valueKey === 'uploaderInfoModel'"
                    ></ImageUpload>
                    <FooterTipView v-if="item.tipShow" :tip-content="getCurrentTipContent(item)" :tip-link="getCurrentTipLink(item)" class="tip-view"></FooterTipView>
                  </FormItem>
                </div>
              </section>

              <!-- 单选框 -->
              <section v-if="item.type === 'RadioGroup'">
                <div :class="['form-item-box', `border-radius-${item.layout}`]">
                  <FormItem
                    :label="item.label"
                    :name="item.valueKey"
                    :value="formData[item.valueKey]"
                    required
                    :class="['item', `border-${item.layout}`, `border-radius-${item.layout}`]"
                    :required-style="{color: 'red'}"
                  >

                    <ConfigProvider :color-mode="colorMode">
                      <RadioGroup v-model="formData[item.valueKey]" @change="(value) => handleChange(value, item)">
                        <Radio v-for="options in item.options" :key="options.value" :name="options.value" :disabled="isPreview">{{ options.label }}</Radio>
                      </RadioGroup>
                    </ConfigProvider>
                  </FormItem>
                </div>
              </section>

              <FooterTipView v-if="item.footerTipShow" :tip-content="item.tipContent" :tip-link="item.tipLink" class="footer-tip-view"></FooterTipView>
            </article>
          </Form>
        </div>

        <ConfigProvider :color-mode="colorMode">
          <Sheets
            :visible="sheetsShow"
            :cancel="false"
            :close="true"
            :close-type="SheetsType.SheetsActionType.icon"
            :mask="true"
            :mask-mode="SheetsType.SheetsMaskMode.dark"
            :header-style="{padding: 0}"
            z-index="999"
            @cancel="sheetsClick"
          >
            <template #header>
              <div class="sheets-header">
                <p class="sheets-title">门店经营主体</p>
              </div>
            </template>
            <div v-for="(item, index) in businessEntityInfo" :key="item.value" :class="['sheets-main', index !== businessEntityInfo.length - 1 ? 'sheets-border-bottom' : '']" @click="sheetsChange(item)">
              <span class="sheets-title">{{ item.title }}</span>
              <span v-if="item.notes" class="sheets-notes">{{ item.notes }}</span>
            </div>
            <div class="divider"></div>
            <p class="cancelPBtn" @click="sheetsClick">
              取消
            </p>
          </Sheets>
        </ConfigProvider>

      </ConfigProvider>
    </main>
    <footer>
      <ConfigProvider :color-mode="colorMode">
        <Button
          type="primary"
          :variant="isSubmitDisabled ? 'disabled' : 'fill'"
          block
          class="btnBorderRadio8"
          style="height: 44px"
          :disabled="isSubmitDisabled"
          @click="shopDecorateSubmit"
        >
          {{ submitButtonText }}
        </Button>
      </ConfigProvider>
    </footer>
  </div>
</template>

<script setup lang="ts">
  // SDK
  import {
    Form,
    FormItem,
    FormType,
    Text,
    Button,
    ConfigProvider,
    Sheets,
    SheetsType,
    Radio,
    RadioGroup,
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 数据
  import { useStore } from 'vuex'

  // 组件
  import Header from '~/components/header/index.vue'
  import FooterTipView from './components/footerTipView/index.vue'
  import ImageUpload from './components/ImageUpload/index.vue'

  // composable
  import { useClaimStorePage } from './composable'

  // 静态资源
  import '~/assets/svg/back.svg'
  import '~/assets/svg/arrowRightRightM.svg'

  // 初始化vuex
  const store = useStore()
  const {
    colorMode,
  } = store.state.claimStore

  // 使用 composable
  const {
    // 数据
    formData,
    sheetsShow,
    formRules,
    businessEntityInfo,
    prohibitOperation,

    // 计算属性
    businessEntityText,
    visibleFormItems,
    isSubmitDisabled,
    isPreview,
    submitButtonText,

    // 方法
    getCurrentTipContent,
    getCurrentTipLink,
    textAndIconChange,
    sheetsClick,
    sheetsChange,
    handleChange,
    shopDecorateSubmit,
  } = useClaimStorePage()

</script>

<style lang="stylus" scoped>
@import '@xhs/water-kaipingyidongBduan/index.css'; // 主题商店样式

// 公共
p
  margin-block-start 0
  margin-block-end 0

.onix-icon-16
  width 16px
  height 16px

.btnBorderRadio8
  border-radius: 8px !important

.form-item-layout
  margin-bottom 20px

.border-radius-top
  border-radius 8px 8px 0 0
  margin-bottom 0 !important

.border-radius-center
  border-radius 0

.border-radius-bottom
  border-radius 0 0 8px 8px

.border-radius-default
  border-radius 8px

.border-center, .border-bottom
  border-top 0.5px solid rgba(0, 0, 0, 0.08)

.sheets-header
  width 100%
  display flex
  justify-content center
  align-items center
  border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

  .sheets-title
    padding 12px 16px
    color rgba(0, 0, 0, 0.45)
    font-size 12px
    font-style normal
    font-weight 400
    line-height 18px

.sheets-main
  display flex
  width 100%
  padding 16px
  flex-direction column
  justify-content center
  align-items center
  gap 8px

  .sheets-title
    color rgba(0, 0, 0, 0.80)
    text-align center
    font-family "PingFang SC"
    font-size 16px
    font-style normal
    font-weight 400
    line-height 24px

  .sheets-notes
    color rgba(0, 0, 0, 0.45)
    text-align center
    font-family "PingFang SC"
    font-size 14px
    font-style normal
    font-weight 400
    line-height 20px

.sheets-border-bottom
    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

.divider
  width 100%
  height 8px
  background var(--Light-Backgrounds-Bg-1, #F5F5F5)

.cancelPBtn
  display flex
  width 100%
  padding 16px
  flex-direction column
  justify-content center
  align-items center
  gap 8px
  color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
  text-align center
  font-family "PingFang SC"
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px
  @supports (padding-bottom: constant(safe-area-inset-bottom))
    padding-bottom constant(safe-area-inset-bottom)
  @supports (padding-bottom: env(safe-area-inset-bottom))
    padding-bottom env(safe-area-inset-bottom)

// 页面
.claim-store-div
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5
  padding-top 24px
  // iOS 安全区域适配
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

  header
    flex 0 0 auto

  main
    flex 1 1 0
    min-height 0
    padding: 0 16px
    margin-top 16px
    overflow auto
    -webkit-overflow-scrolling touch

    .form-item
      width 100%

      .title
        margin 0 0 12px 8px
        color rgba(0, 0, 0, 0.62)
        font-family "PingFang SC"
        font-size 12px
        font-style normal
        font-weight 400
        line-height 18px

      .form-item-box
        width 100%
        height 100%
        padding-left 16px
        background #FFF

        .item
          display flex
          width 100%
          padding 12px 16px 12px 0
          margin-bottom 0
          flex-direction column
          align-items flex-start

          .text-field
            width 100%
            height 100%
            padding 0

          .sheets
            width 100%
            height 100%
            padding 0
            display flex
            align-items center
            justify-content space-between

            .multiLineText
              width 100%
              height 100%
              padding 0
              display flex
              flex-direction: column
              justify-content: center
              color rgba(0, 0, 0, 0.8)
              font-family "PingFang SC"
              font-size 16px
              font-style normal
              font-weight 400
              line-height 24px

            .multiLineText-shopAddress
              color rgba(0, 0, 0, 0.45)
              font-size 14px
              line-height 22px

            .sheets-empty
              width 100%
              color rgba(0, 0, 0, 0.45)
              font-family "PingFang SC"
              font-size 16px
              font-style normal
              font-weight 400
              line-height 24px

    .tip-view
      margin-top 12px

    .footer-tip-view
      padding 8px 0 12px 8px

  footer
    flex 0 0 auto
    width 100%
    min-height 60px
    padding: 8px 16px
    background #fff
    @supports (padding-bottom: env(safe-area-inset-bottom))
      padding-bottom env(safe-area-inset-bottom)
    @supports (padding-bottom: constant(safe-area-inset-bottom))
      padding-bottom constant(safe-area-inset-bottom)

</style>
