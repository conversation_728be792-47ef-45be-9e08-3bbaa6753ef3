<template>
  <div class="category-picker-wrapper">
    <!-- 触发器 -->
    <div
      class="category-picker-trigger"
      :class="{
        'disabled': disabled,
        'readonly': readonly,
        'has-value': currentValue
      }"
      @click="showPicker"
    >
      <div class="trigger-content">
        <span class="trigger-text" :class="{ 'placeholder': !currentValue || currentValue.length === 0 }">
          {{ displayText }}
        </span>
        <OnixIcon
          name="arrowRightRightM"
          class="trigger-icon"
        />
      </div>
    </div>

    <!-- Sheets 选择器 -->
    <Sheets
      :visible="visible"
      :title="title || '选择主营品类'"
      :size="SheetsType.SheetsSize.full"
      :auto-size="70"
    >
      <template #header>
        <div class="header-content">
          <span class="header-title">{{ title }}</span>
          <Icon icon-name="Close" :size="20" class="close-icon" @click="handleCancel" />
        </div>
      </template>
      <div class="category-picker-content">
        <!-- 分类列表 - Grid布局按层级分列 -->
        <div v-if="hasAnyData" class="category-grid-container" :style="{ gridTemplateColumns: `repeat(${allLevelsData.length}, 1fr)` }">
          <div
            v-for="levelData in allLevelsData"
            :key="levelData.level"
            class="level-column"
          >
            <!-- 该层级的分类列表 -->
            <div class="category-list">
              <!-- 最后一层使用 CheckBox 样式的分类项 -->
              <template v-if="isLastLevelByNumber(levelData.level) && levelData.categories.length > 0">
                <div
                  v-for="category in levelData.categories"
                  :key="category.id"
                  class="category-item checkbox-item"
                  :class="{
                    'selected': isCategorySelected(category, levelData)
                  }"
                  @click="handleCheckBoxItemClick(category, levelData)"
                >
                  <div class="category-content">
                    <div class="category-name">{{ category.name }}</div>
                    <div class="checkbox-indicator">
                      <CheckBox
                        v-model="currentValue[category.level - 1]"
                        :value="category.id"
                        :is-control="true"
                        :size="CheckBoxGroupType.CheckBoxGroupSize.SMALL"
                        :disabled="true"
                      />
                    </div>
                  </div>
                </div>
              </template>

              <!-- 非最后一层使用普通分类项 -->
              <template v-else-if="levelData.categories.length > 0">
                <div
                  v-for="category in levelData.categories"
                  :key="category.id"
                  class="category-item"
                  :class="{
                    'selected': isSelected(category, levelData.level)
                  }"
                  @click="handleCategoryClick(category, levelData)"
                >
                  <div class="category-content">
                    <div class="category-name">{{ category.name }}</div>
                  </div>
                </div>
              </template>

              <!-- 空层级占位 -->
              <div v-if="levelData.categories.length === 0" class="empty-level">
                <div class="empty-text">暂无数据</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 整个浮层没有数据时显示 Empty 组件 -->
        <Empty v-if="!hasAnyData" description="暂无分类数据" />

        <!-- 确认按钮 -->
        <div class="confirm-section">
          <Button
            type="primary"
            variant="fill"
            block
            :disabled="!hasValidSelection"
            @click="handleConfirm"
          >
            确认选择
          </Button>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <div class="loading-content">
            <OnixIcon name="loading" class="loading-icon" />
            <span>加载中...</span>
          </div>
        </div>
      </div>
    </Sheets>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    Sheets,
    Icon,
    SheetsType,
    Button,
    Empty,
    CheckBox,
    CheckBoxGroupType
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import { useCategoryPicker } from './composable'
  import type {
    CategoryPickerProps,
    CategoryPickerEmits,
    ICategory,
    LevelData
    } from './types'

  // 静态资源 - 请根据项目实际路径调整
  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<CategoryPickerProps>(), {
    modelValue: () => [],
    placeholder: '请选择主营品类',
    disabled: false,
    readonly: false,
    title: '选择主营品类',
    maxLevels: 3,
    lastLevelMultiple: false,
    gridColumns: 3
  })

  // Emits
  const emit = defineEmits<CategoryPickerEmits>()

  // 使用组合式函数
  const {
    loading,
    visible,
    displayText,
    currentValue,
    handleCategoryClick,
    handleConfirm,
    handleCancel,
    showPicker,
    levelsData,
    maxLevels,
    isLastLevelByNumber
  } = useCategoryPicker(props, emit)

  // 获取所有层级数据 - 根据 maxLevels 填充空层级
  const allLevelsData = computed(() => {
    const result: LevelData[] = []
    const actualLevels = levelsData.value || []

    // 根据 maxLevels 创建固定数量的层级
    for (let i = 1; i <= maxLevels.value; i++) {
      const existingLevel = actualLevels.find(level => level.level === i)
      if (existingLevel) {
        // 使用实际存在的层级数据
        result.push(existingLevel)
      } else {
        // 创建空的层级数据
        result.push({
          level: i,
          categories: [],
          selectedId: undefined,
          selectedIds: undefined
        })
      }
    }

    return result
  })

  // 判断是否有有效选择
  const hasValidSelection = computed(() =>
    allLevelsData.value.some(levelData =>
      levelData.selectedId || (levelData.selectedIds && levelData.selectedIds.length > 0)))

  // 判断是否有任何数据
  const hasAnyData = computed(() => allLevelsData.value.some(levelData => levelData.categories.length > 0))

  // 判断单个分类是否被选中 - 基于 modelValue 判断
  const isCategorySelected = (category: ICategory, levelData: LevelData): boolean => {
    if (!currentValue.value || currentValue.value.length === 0) {
      return false
    }

    // 计算当前层级在 modelValue 数组中的索引位置
    const levelIndex = levelData.level - 1

    if (levelIndex >= currentValue.value.length) {
      return false
    }

    const valueAtLevel = currentValue.value[levelIndex]

    // 如果是最后一层且支持多选
    if (isLastLevelByNumber(levelData.level) && props.lastLevelMultiple) {
      // 多选模式：解析逗号分隔的字符串，检查是否包含当前 category.id
      if (valueAtLevel) {
        const selectedIds = valueAtLevel.split(',')
        return selectedIds.includes(category.id || '')
      }
      return false
    }

    // 单选模式：检查对应位置的值是否等于当前 category.id
    return valueAtLevel === category.id
  }
  // 处理 CheckBox 样式分类项的点击 - 直接更新 modelValue
  const handleCheckBoxItemClick = (category: ICategory, levelData: LevelData) => {
    console.log('handleCheckBoxItemClick', category, levelData, currentValue.value)
    if (!currentValue.value) {
      currentValue.value = []
    }

    const levelIndex = levelData.level - 1
    const isCurrentlySelected = isCategorySelected(category, levelData)

    // 创建新的值数组，确保长度足够
    const newValue: string[] = [...currentValue.value]
    while (newValue.length <= levelIndex) {
      newValue.push('')
    }

    // 如果是最后一层且支持多选
    if (isLastLevelByNumber(levelData.level) && props.lastLevelMultiple) {
      // 多选模式：将多个选中项用逗号分隔存储在一个字符串中
      const currentLevelValue = newValue[levelIndex]

      // 解析当前层级的值为数组
      const currentIds = currentLevelValue ? currentLevelValue.split(',') : []

      if (!isCurrentlySelected) {
        // 选中：添加到数组中
        if (!currentIds.includes(category.id || '')) {
          currentIds.push(category.id || '')
        }
      } else {
        // 取消选中：从数组中移除
        const index = currentIds.indexOf(category.id || '')
        if (index > -1) {
          currentIds.splice(index, 1)
        }
      }

      // 将数组重新序列化为逗号分隔的字符串
      newValue[levelIndex] = currentIds.filter(id => id).join(',')
    } else if (!isCurrentlySelected) {
      // 单选模式 - 选中：设置为当前值，并清空后续层级
      newValue[levelIndex] = category.id || ''
      // 清空后续层级
      newValue.splice(levelIndex + 1)
    } else {
      // 单选模式 - 取消选中：清空当前及后续层级
      newValue.splice(levelIndex)
    }

    // 更新 currentValue 并触发事件
    currentValue.value = newValue
  }

  // 判断分类是否被选中
  const isSelected = (category: ICategory, level?: number): boolean => {
    if (level) {
      // 查找指定层级的数据
      const levelData = allLevelsData.value.find(ld => ld.level === level)
      if (!levelData) return false

      // 如果是最后一层且支持多选
      if (isLastLevelByNumber(level) && props.lastLevelMultiple) {
        return levelData.selectedIds?.includes(category.id || '') || false
      }

      // 单选模式
      return levelData.selectedId === category.id
    }

    return false
  }

</script>

<style scoped lang="stylus">
.category-picker-wrapper
  flex 1
.category-picker-trigger
  display flex
  align-items center
  justify-content space-between
  &.disabled
    cursor not-allowed
    opacity 0.6

  &.readonly
    cursor default

.trigger-content
  display flex
  align-items center
  justify-content space-between
  flex 1

.trigger-text
  font-size 16px
  line-height 1.4
  flex 1

.trigger-icon
  font-size 16px
  margin-left 8px
  transition transform 0.2s ease

.category-picker-content
  padding-bottom 10px
  position relative
  height 100%
  background #FFFFFF

.breadcrumb-nav
  display flex
  align-items center
  margin-bottom 16px
  padding 8px 0
  border-bottom 1px solid var(--border-color, #e0e0e0)
  flex-wrap wrap

.breadcrumb-item
  display flex
  align-items center
  font-size 14px
  color var(--text-secondary, #666666)
  cursor pointer
  padding 4px 8px
  border-radius 4px
  transition all 0.2s ease

  &:hover
    background var(--bg-hover, #f5f5f5)

  &.active
    color var(--primary-color, #1890ff)
    font-weight 500

.breadcrumb-arrow
  margin 0 4px
  font-size 12px
  color var(--text-tertiary, #999999)

.category-grid-container
  display grid
  max-height 400px
  overflow-y auto
  margin-bottom 16px

.level-column
  display flex
  flex-direction column
  border-right 2px solid #F5F5F5

  &:last-child
    border-right none
    padding-right 0

.level-header
  font-size 14px
  font-weight 500
  color var(--text-secondary, #666666)
  padding 8px 12px
  background var(--bg-secondary, #f8f9fa)
  border-radius 6px
  margin-bottom 12px
  text-align center
  border-left 3px solid var(--primary-color, #1890ff)

.category-list
  display flex
  flex-direction column
  flex 1
  min-height 0

.category-item
  display flex
  align-items center
  padding 12px 8px
  margin-bottom 4px
  transition all 0.2s ease
  background var(--bg)
  position relative

  &.selected
    background: #F5F5F5;
    color: var(--Light-Labels-Title, rgba(0, 0, 0, 0.80));
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 125% */
.category-content
  display flex
  align-items center
  justify-content space-between
  width 100%
  position relative

.category-name
  font-size 14px
  line-height 1.4
  color var(--text-color, #333333)
  flex 1
  text-align center

.select-indicator
  display flex
  align-items center
  justify-content center

.check-circle
  width 20px
  height 20px
  background var(--primary-color, #ff4757)
  border-radius 50%
  display flex
  align-items center
  justify-content center
  color white
  font-size 12px

.arrow-indicator
  display flex
  align-items center
  justify-content center
  color var(--text-tertiary, #999999)
  font-size 14px

.confirm-section
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background var(--bg, #ffffff)
  border-top 1px solid var(--border-color, #e0e0e0)
  z-index 100

.confirm-button
  width 100%
  padding 16px
  background var(--primary-color, #1890ff)
  color white
  border none
  border-radius 8px
  font-size 16px
  font-weight 500
  cursor pointer
  transition all 0.2s ease

  &:hover:not(:disabled)
    background var(--primary-color-hover, #40a9ff)

  &:disabled
    background var(--bg-disabled, #f5f5f5)
    color var(--text-disabled, #bfbfbf)
    cursor not-allowed

.loading-overlay
  position absolute
  top 0
  left 0
  right 0
  bottom 0
  background rgba(255, 255, 255, 0.8)
  display flex
  align-items center
  justify-content center
  z-index 10

.loading-content
  display flex
  flex-direction column
  align-items center
  font-size 14px
  color var(--text-secondary, #666666)

.loading-icon
  font-size 24px
  margin-bottom 8px
  animation spin 1s linear infinite

@keyframes spin
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)

.retry-button
  background var(--primary-color, #1890ff)
  color white
  border none
  border-radius 4px
  padding 4px 8px
  font-size 12px
  cursor pointer
  margin-left 8px

  &:hover
    background var(--primary-color-hover, #40a9ff)
.header-content
  display flex
  align-items center
  width 100%
  padding 12px
  background #F5F5F5
  .header-title
    font-size 16px
    font-weight 500
    flex 1
    text-align center
  .close-icon
    font-size 16px
    color var(--icon-color, #666666)
    transition transform 0.2s ease
    cursor pointer

.empty-level
  display flex
  align-items center
  justify-content center
  min-height 80px
  color var(--text-tertiary, #999999)

.empty-text
  font-size 14px
  text-align center

// CheckBox 样式的分类项
.checkbox-item
  // 继承普通分类项的样式
  .category-content
    display flex
    align-items center
    justify-content space-between

  .checkbox-indicator
    display flex
    align-items center
    justify-content center
</style>
