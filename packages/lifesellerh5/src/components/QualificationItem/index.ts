// 导入类型
import type { QualificationImageItem } from '../QualificationImage/types'
import type { ValidityTimePickerValue } from '../ValidityTimePicker/types'

// 导出主组件
export { default as QualificationItmeForLocalClaim } from './QualificationItmeForLocalClaim.vue'

// 重新导出相关类型（如果需要在外部使用）
export type { QualificationImageItem } from '../QualificationImage/types'
export type { ValidityTimePickerValue } from '../ValidityTimePicker/types'

// 导出组件相关的数据结构类型
export interface QualificationItemData {
  id?: string | number
  titleLabel?: string
  qualificationCode?: string
  qualificationName?: string
  images?: QualificationImageItem[]
  validity?: ValidityTimePickerValue | null
  validityDays?: number | null
  validityError?: string
  status?: boolean
  statusText?: string
  statusClass?: string
  optional?: boolean
}

// 导出事件类型
export interface QualificationItmeForLocalClaimEmits {
  'update:modelValue': [value: QualificationItemData[]]
  'update:supplementaryImages': [value: QualificationImageItem[]]
  'change': [type: 'qualification' | 'supplementary', index?: number, value?: any]
  'upload': [type: 'qualification' | 'supplementary', index?: number, file?: File]
  'delete': [type: 'qualification' | 'supplementary', index?: number, imageIndex?: number]
}

// 导出组件属性类型
export interface QualificationItmeForLocalClaimProps {
  modelValue?: QualificationItemData[]
  supplementaryImages?: QualificationImageItem[]
  showSupplementaryMaterials?: boolean
  readonly?: boolean
  disabled?: boolean
}
