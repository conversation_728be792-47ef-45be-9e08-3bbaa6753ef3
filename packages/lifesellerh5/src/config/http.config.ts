/* eslint no-template-curly-in-string: 0 */

import { HttpConfig } from '@xhs/launcher'
// import { getLoganUrlByQuery } from 'shared/logan'

const httpConfig: HttpConfig = {
  BASE_URL: {
    // development: getLoganUrlByQuery(),
    // TODO:记得改回去
    // development: 'http://logan.devops.xiaohongshu.com/proxy/life-poiclaim-sit',
    // development: 'http://logan.devops.xiaohongshu.com/proxy/life-poiclaim-beta',
    development: 'http://logan.devops.xiaohongshu.com/proxy/zhangyang7-life-sit',
    // development: 'http://life-dig.sl.sit.xiaohongshu.com',
    // formula build -e test
    test: '',
    // formula build -e prerelease
    prerelease: '',
    // formula build
    production: '',
  },
  API_LIST: {
    GET_REDIRECT_ID: '/api/edith/general/mapping',
    GET_POI_FEEDBACK_INFO: '/api/redlife/local/poi/info',
    SUBMIT_POI_FEEDBACK: '/api/redlife/poi/user/feedback/submit',
    GET_UPLOAD_TOKEN: '/api/redlife/merchant/upload/permit',
    GET_UPLOADER_TOKEN: '/api/life/api/edith/upload/web/permit', // 上传 ✅
    // ==========门店==========
    PROF_UPLOAD_TOKEN: '/api/edith/app/userqms/prof/upload/token', // 新版获取图片上传token
    PROF_FILE_REPLACE: '/api/edith/app/userqms/prof/file/replace', // 获取图片永久地址
    POI_SUBMIT_BRAND_POI_REVIEWS: '/api/edith/app/poi/submit_brand_poi_reviews', // 提交门店装修
    POI_GET_BRAND_POI_REVIEWS: '/api/edith/app/poi/get_brand_poi_reviews', // 门店信息点查
    POI_SUBMIT_APPLY: '/api/edith/app/businesscenter/poi/submitapply', // 认领门店提交
    POI_GET_BIZCOMPANY_INFO: '/api/edith/app/businesscenter/poi/getbizcompanyinfo', // 获取当前用户营业执照
    POI_APPLY_DETAIL: '/api/edith/app/businesscenter/poi/applydetail' // 认领门店详情
  },
  BASE_CONFIG: {
    defaults: {
      timeout: process.env.BROWSER ? 10000 : 3000,
      useBridge: true,
      preferBridge: true,
      headers: {
        'Content-Type': 'application/json',
      },
    },
    development: {
      withCredentials: true,
    },
  },
}

export default httpConfig
